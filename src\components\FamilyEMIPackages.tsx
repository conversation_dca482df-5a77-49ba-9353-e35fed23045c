import React, { useState, useEffect } from 'react';
import { 
  Package, 
  Users, 
  CreditCard, 
  Star,
  Car,
  Bed,
  Eye,
  Search,
  Filter,
  TrendingUp,
  AlertCircle
} from 'lucide-react';
import { createClient } from '@supabase/supabase-js';
import { getQuoteSupabaseConfig } from '../config/env';

interface FamilyEMIPackage {
  id: string;
  quote_id: string;
  family_type_id: string;
  family_type_name: string;
  no_of_adults: number;
  no_of_children: number;
  no_of_child: number;
  no_of_infants: number;
  family_count: number;
  rooms_need: number;
  cab_type: string;
  cab_capacity: number;
  destination_category: string;
  season_category: string;
  package_duration_days: number;
  hotel_cost: number;
  vehicle_cost: number;
  additional_costs: number;
  subtotal: number;
  total_price: number;
  discount_amount: number;
  emi_enabled: boolean;
  min_emi_months: number;
  max_emi_months: number;
  emi_processing_fee_percent: number;
  emi_interest_rate_percent: number;
  is_public_visible: boolean;
  public_display_order: number;
  created_at: string;
  updated_at: string;
  emi_plans: EMIPlan[];
  quotes?: {
    id: string;
    destination: string;
    package_name: string;
    customer_name: string;
    trip_duration: number;
    total_cost: number;
  };
}

interface EMIPlan {
  id: string;
  family_price_id: string;
  emi_months: number;
  monthly_amount: number;
  total_amount: number;
  processing_fee: number;
  total_interest: number;
  first_payment_amount?: number;
  subsequent_payment_amount?: number;
  final_payment_amount?: number;
  savings_vs_full_payment: number;
  effective_annual_rate: number;
  is_featured: boolean;
  marketing_label?: string;
  created_at: string;
  updated_at: string;
}

// Helper function to get user-friendly family type labels
const getFamilyTypeLabel = (familyTypeId: string, familyTypeName: string) => {
  const labelMap: { [key: string]: string } = {
    'SD': '👫 Single/Double (1-2 Adults)',
    'SF': '👨‍👩‍👧‍👦 Small Family (2 Adults + 1-2 Kids)',
    'MF': '👨‍👩‍👧‍👦 Medium Family (2 Adults + 2-3 Kids)',
    'LF': '👨‍👩‍👧‍👦 Large Family (3+ Adults + Kids)',
    'COUPLE': '💑 Couple (2 Adults)',
    'FAMILY_3': '👨‍👩‍👧 Family of 3',
    'FAMILY_4': '👨‍👩‍👧‍👦 Family of 4',
    'FAMILY_5': '👨‍👩‍👧‍👦 Family of 5+',
    'GROUP': '👥 Group (6+ People)'
  };

  return labelMap[familyTypeId] || familyTypeName || familyTypeId;
};

const FamilyEMIPackages: React.FC = () => {
  const [packages, setPackages] = useState<FamilyEMIPackage[]>([]);
  const [filteredPackages, setFilteredPackages] = useState<FamilyEMIPackage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [destinationFilter, setDestinationFilter] = useState('all');
  const [familyTypeFilter, setFamilyTypeFilter] = useState('all');
  const [selectedPackage, setSelectedPackage] = useState<FamilyEMIPackage | null>(null);
  const [showEMIModal, setShowEMIModal] = useState(false);
  const [availableDestinations, setAvailableDestinations] = useState<string[]>([]);
  const [availableFamilyTypes, setAvailableFamilyTypes] = useState<{id: string, name: string}[]>([]);
  const [priceRange, setPriceRange] = useState<{min: number, max: number}>({min: 0, max: 500000});
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(12); // Show 12 packages per page
  const [totalCount, setTotalCount] = useState(0);
  const [showFilters, setShowFilters] = useState(true);

  // Create Quote database client for accessing family_type_prices table
  const quoteConfig = getQuoteSupabaseConfig();
  const quoteSupabase = createClient(quoteConfig.url, quoteConfig.anonKey);

  const loadFamilyEMIPackages = async (resetPage = false) => {
    try {
      setLoading(true);
      setError(null);
      
      if (resetPage) {
        setCurrentPage(1);
      }

      const pageToLoad = resetPage ? 1 : currentPage;
      const from = (pageToLoad - 1) * pageSize;
      const to = from + pageSize - 1;

      // Build query with filters - Join with quotes table to get destination and package info
      let query = quoteSupabase
        .from('family_type_prices')
        .select(`
          id,
          quote_id,
          family_type_id,
          family_type_name,
          no_of_adults,
          no_of_children,
          no_of_child,
          no_of_infants,
          family_count,
          rooms_need,
          cab_type,
          cab_capacity,
          destination_category,
          season_category,
          package_duration_days,
          hotel_cost,
          vehicle_cost,
          additional_costs,
          subtotal,
          total_price,
          discount_amount,
          emi_enabled,
          min_emi_months,
          max_emi_months,
          emi_processing_fee_percent,
          emi_interest_rate_percent,
          is_public_visible,
          public_display_order,
          created_at,
          updated_at,
          quotes (
            id,
            destination,
            package_name,
            customer_name,
            trip_duration,
            total_cost
          )
        `, { count: 'exact' })
        .eq('emi_enabled', true)
        .eq('is_public_visible', true);

      // Apply filters
      if (destinationFilter !== 'all') {
        // Filter by either destination_category or quotes.destination
        query = query.or(`destination_category.eq.${destinationFilter},quotes.destination.eq.${destinationFilter}`);
      }

      if (familyTypeFilter !== 'all') {
        query = query.eq('family_type_id', familyTypeFilter);
      }

      if (searchQuery) {
        // Enhanced search to include quotes.destination and quotes.package_name
        query = query.or(`family_type_name.ilike.%${searchQuery}%,destination_category.ilike.%${searchQuery}%,family_type_id.ilike.%${searchQuery}%,quotes.destination.ilike.%${searchQuery}%,quotes.package_name.ilike.%${searchQuery}%`);
      }

      // Apply price range filter
      if (priceRange.min > 0) {
        query = query.gte('total_price', priceRange.min);
      }
      if (priceRange.max < 500000) {
        query = query.lte('total_price', priceRange.max);
      }

      // Apply pagination and ordering
      const { data: familyPricesData, error: familyPricesError, count } = await query
        .order('public_display_order', { ascending: true })
        .order('created_at', { ascending: false })
        .range(from, to);

      if (familyPricesError) {
        throw familyPricesError;
      }

      // Set total count for pagination
      setTotalCount(count || 0);

      if (!familyPricesData || familyPricesData.length === 0) {
        console.log('No family EMI packages found for current filters');
        setPackages([]);
        setFilteredPackages([]);
        return;
      }

      // Get EMI plans for all packages
      const packageIds = familyPricesData.map(pkg => pkg.id);
      const { data: emiPlansData, error: emiPlansError } = await quoteSupabase
        .from('family_type_emi_plans')
        .select(`
          id,
          family_price_id,
          emi_months,
          monthly_amount,
          total_amount,
          processing_fee,
          total_interest,
          first_payment_amount,
          subsequent_payment_amount,
          final_payment_amount,
          savings_vs_full_payment,
          effective_annual_rate,
          is_featured,
          marketing_label,
          created_at,
          updated_at
        `)
        .in('family_price_id', packageIds)
        .order('emi_months', { ascending: true });

      if (emiPlansError) {
        throw emiPlansError;
      }

      // Combine packages with their EMI plans
      const packagesWithEMI: FamilyEMIPackage[] = familyPricesData.map(pkg => ({
        ...pkg,
        emi_plans: emiPlansData?.filter(plan => plan.family_price_id === pkg.id) || []
      }));

      console.log(`Loaded ${packagesWithEMI.length} family EMI packages from database (Page ${pageToLoad})`);
      setPackages(packagesWithEMI);
      setFilteredPackages(packagesWithEMI); // Since we're filtering server-side, these are already filtered
      
    } catch (error: any) {
      console.error('Error loading family EMI packages:', error);
      setError(error.message || 'Failed to load family EMI packages');
    } finally {
      setLoading(false);
    }
  };

  const loadFilterOptions = async () => {
    try {
      // Get unique destinations from quotes table via family_type_prices join
      const { data: destinationsData, error: destinationsError } = await quoteSupabase
        .from('family_type_prices')
        .select(`
          destination_category,
          quotes (
            destination
          )
        `)
        .eq('emi_enabled', true)
        .eq('is_public_visible', true)
        .not('destination_category', 'is', null);

      if (destinationsError) {
        console.error('Error loading destinations:', destinationsError);
      } else {
        // Get unique destinations from both destination_category and quotes.destination
        const destinationSet = new Set<string>();
        destinationsData?.forEach(item => {
          if (item.destination_category) {
            destinationSet.add(item.destination_category);
          }
          if (item.quotes?.destination) {
            destinationSet.add(item.quotes.destination);
          }
        });
        const uniqueDestinations = Array.from(destinationSet).filter(Boolean).sort();
        setAvailableDestinations(uniqueDestinations);
      }

      // Get unique family types
      const { data: familyTypesData, error: familyTypesError } = await quoteSupabase
        .from('family_type_prices')
        .select('family_type_id, family_type_name')
        .eq('emi_enabled', true)
        .eq('is_public_visible', true);

      if (familyTypesError) {
        console.error('Error loading family types:', familyTypesError);
      } else {
        const uniqueFamilyTypes = familyTypesData?.reduce((acc: {id: string, name: string}[], curr) => {
          if (!acc.find(item => item.id === curr.family_type_id)) {
            acc.push({ id: curr.family_type_id, name: curr.family_type_name });
          }
          return acc;
        }, []) || [];
        setAvailableFamilyTypes(uniqueFamilyTypes);
      }
    } catch (error) {
      console.error('Error loading filter options:', error);
    }
  };

  const refreshData = async () => {
    await loadFamilyEMIPackages(true);
    await loadFilterOptions();
  };

  // Load filter options once
  useEffect(() => {
    loadFilterOptions();
  }, []);

  // Load packages initially
  useEffect(() => {
    loadFamilyEMIPackages(true);
  }, []);

  // Reload when filters change (server-side filtering)
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      loadFamilyEMIPackages(true); // Reset to page 1 when filters change
    }, 300); // Debounce for 300ms

    return () => clearTimeout(timeoutId);
  }, [searchQuery, destinationFilter, familyTypeFilter, priceRange]);

  // Load new page when currentPage changes
  useEffect(() => {
    if (currentPage > 1) {
      loadFamilyEMIPackages(false);
    }
  }, [currentPage]);

  // Helper functions for pagination
  const totalPages = Math.ceil(totalCount / pageSize);
  const startIndex = (currentPage - 1) * pageSize + 1;
  const endIndex = Math.min(currentPage * pageSize, totalCount);

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString()}`;
  };

  const getFamilyComposition = (pkg: FamilyEMIPackage) => {
    const parts = [];
    if (pkg.no_of_adults > 0) parts.push(`${pkg.no_of_adults}A`);
    if (pkg.no_of_children > 0) parts.push(`${pkg.no_of_children}C`);
    if (pkg.no_of_child > 0) parts.push(`${pkg.no_of_child}K`);
    if (pkg.no_of_infants > 0) parts.push(`${pkg.no_of_infants}I`);
    return parts.join(' + ');
  };

  const getDestinationIcon = (category: string) => {
    if (!category) return '📍';
    switch (category.toLowerCase()) {
      case 'beach': return '🏖️';
      case 'hill station': return '⛰️';
      case 'adventure': return '🏔️';
      case 'heritage': return '🏛️';
      case 'wildlife': return '🦁';
      case 'city': return '🏙️';
      case 'religious': return '🕉️';
      default: return '📍';
    }
  };

  const getSeasonBadgeColor = (season: string) => {
    if (!season) return 'bg-gray-100 text-gray-800';
    switch (season.toLowerCase()) {
      case 'peak': return 'bg-red-100 text-red-800';
      case 'off-peak': return 'bg-green-100 text-green-800';
      case 'normal': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <span className="ml-3 text-gray-600">Loading family EMI packages...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <div className="flex items-center">
            <AlertCircle className="w-6 h-6 text-red-600 mr-3" />
            <div>
              <h3 className="text-lg font-medium text-red-800">Error Loading Data</h3>
              <p className="text-red-700 mt-1">{error}</p>
              <button
                onClick={refreshData}
                className="mt-3 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-xl font-bold text-gray-800">Available Family EMI Packages</h2>
          <p className="text-gray-600">Browse family vacation packages with EMI options from database</p>
          <div className="mt-2 text-sm text-gray-500">
            Showing {startIndex}-{endIndex} of {totalCount} packages {totalPages > 1 && `(Page ${currentPage} of ${totalPages})`}
          </div>
        </div>
        <div className="flex items-center gap-4">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`px-3 py-2 text-sm rounded-lg transition-colors flex items-center gap-2 ${
              showFilters ? 'bg-primary text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <Filter className="w-4 h-4" />
            {showFilters ? 'Hide Filters' : 'Show Filters'}
          </button>
          <button
            onClick={refreshData}
            className="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
          >
            Refresh
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Available</p>
              <p className="text-xl font-bold text-blue-600">{totalCount}</p>
            </div>
            <Package className="w-6 h-6 text-blue-600" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Current Page</p>
              <p className="text-xl font-bold text-green-600">{packages.length}</p>
            </div>
            <CreditCard className="w-6 h-6 text-green-600" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Featured Plans</p>
              <p className="text-xl font-bold text-yellow-600">
                {packages.reduce((count, pkg) => count + pkg.emi_plans.filter(plan => plan.is_featured).length, 0)}
              </p>
            </div>
            <Star className="w-6 h-6 text-yellow-600" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Page Avg. Value</p>
              <p className="text-xl font-bold text-purple-600">
                {packages.length > 0 ? formatCurrency(packages.reduce((sum, pkg) => sum + pkg.total_price, 0) / packages.length) : '₹0'}
              </p>
            </div>
            <TrendingUp className="w-6 h-6 text-purple-600" />
          </div>
        </div>
      </div>

      {/* Enhanced Filters */}
      {showFilters && (
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search packages..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>

            <select
              value={destinationFilter}
              onChange={(e) => setDestinationFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              <option value="all">🌍 All Destinations</option>
              {availableDestinations
                .sort((a, b) => a.localeCompare(b))
                .map(dest => (
                <option key={dest} value={dest}>
                  {getDestinationIcon(dest)} {dest}
                </option>
              ))}
            </select>

            <select
              value={familyTypeFilter}
              onChange={(e) => setFamilyTypeFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              <option value="all">All Family Types</option>
              {availableFamilyTypes
                .sort((a, b) => a.name.localeCompare(b.name))
                .map(ft => (
                <option key={ft.id} value={ft.id}>
                  {getFamilyTypeLabel(ft.id, ft.name)}
                </option>
              ))}
            </select>

            <button
              onClick={() => {
                setSearchQuery('');
                setDestinationFilter('all');
                setFamilyTypeFilter('all');
                setPriceRange({min: 0, max: 500000});
              }}
              className="px-4 py-2 bg-red-50 text-red-600 border border-red-200 rounded-lg hover:bg-red-100 transition-colors"
            >
              Clear Filters
            </button>
          </div>

          {/* Price Range Filter */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Price Range: {formatCurrency(priceRange.min)} - {formatCurrency(priceRange.max)}
              </label>
              <div className="flex gap-2">
                <input
                  type="number"
                  placeholder="Min Price"
                  value={priceRange.min}
                  onChange={(e) => setPriceRange(prev => ({...prev, min: Number(e.target.value) || 0}))}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                />
                <input
                  type="number"
                  placeholder="Max Price"
                  value={priceRange.max}
                  onChange={(e) => setPriceRange(prev => ({...prev, max: Number(e.target.value) || 500000}))}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>
            </div>

            <div className="flex items-end text-sm text-gray-600">
              <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
                <div className="flex items-center gap-2">
                  <Filter className="w-4 h-4 text-blue-600" />
                  <span className="font-medium text-blue-800">
                    {loading ? 'Loading...' : `${totalCount} total packages available`}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Packages Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
        {filteredPackages.map((pkg) => (
          <div key={pkg.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
            {/* Package Header */}
            <div className="p-6 border-b border-gray-100">
              <div className="flex justify-between items-start mb-4">
                <div className="flex items-center gap-2">
                  <span className="text-2xl">{getDestinationIcon(pkg.destination_category)}</span>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800">
                      {pkg.quotes?.destination || pkg.destination_category || 'Unknown Destination'}
                    </h3>
                    <p className="text-sm text-gray-500">
                      {pkg.quotes?.package_name || `${pkg.family_type_name} Package`}
                    </p>
                    <p className="text-xs text-gray-400">
                      {pkg.family_type_name} • {pkg.package_duration_days ? `${pkg.package_duration_days} Days` : 'Duration TBD'}
                    </p>
                  </div>
                </div>
                <div className="flex flex-col items-end gap-1">
                  {pkg.season_category && (
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getSeasonBadgeColor(pkg.season_category)}`}>
                      {pkg.season_category}
                    </span>
                  )}
                  {pkg.emi_plans.some(plan => plan.is_featured) && (
                    <span className="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800 flex items-center gap-1">
                      <Star className="w-3 h-3" />
                      Featured EMI
                    </span>
                  )}
                </div>
              </div>

              {/* Family Composition */}
              <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-1">
                    <Users className="w-4 h-4" />
                    <span>{getFamilyComposition(pkg)}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Bed className="w-4 h-4" />
                    <span>{pkg.rooms_need} Room{pkg.rooms_need > 1 ? 's' : ''}</span>
                  </div>
                  {pkg.cab_type && (
                    <div className="flex items-center gap-1">
                      <Car className="w-4 h-4" />
                      <span>{pkg.cab_type}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Package Summary */}
              {pkg.quotes && (
                <div className="bg-blue-50 p-3 rounded-lg mb-4 border border-blue-200">
                  <div className="text-sm text-blue-800">
                    <div className="font-medium mb-1">📋 Package Details</div>
                    <div className="text-xs text-blue-600 space-y-1">
                      <div>🎯 <strong>Package:</strong> {pkg.quotes.package_name}</div>
                      <div>📍 <strong>Destination:</strong> {pkg.quotes.destination}</div>
                      <div>⏱️ <strong>Duration:</strong> {pkg.package_duration_days || pkg.quotes.trip_duration || 'TBD'} days</div>
                    </div>
                  </div>
                </div>
              )}

              {/* Pricing */}
              <div className="mb-4">
                <div className="flex items-center justify-between">
                  <div>
                    {pkg.discount_amount > 0 && (
                      <span className="text-sm text-gray-500 line-through">
                        {formatCurrency(pkg.subtotal)}
                      </span>
                    )}
                    <div className="text-2xl font-bold text-gray-800">{formatCurrency(pkg.total_price)}</div>
                  </div>
                  {pkg.discount_amount > 0 && (
                    <div className="text-right">
                      <span className="bg-green-100 text-green-800 text-sm font-medium px-2 py-1 rounded">
                        Save {formatCurrency(pkg.discount_amount)}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* EMI Plans Preview */}
            <div className="p-6">
              <h4 className="text-sm font-semibold text-gray-800 mb-3 flex items-center gap-2">
                <CreditCard className="w-4 h-4" />
                EMI Options ({pkg.emi_plans.length} available)
              </h4>
              
              {pkg.emi_plans.length > 0 ? (
                <>
                  <div className="space-y-2">
                    {pkg.emi_plans.slice(0, 2).map((plan) => (
                      <div key={plan.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="text-center">
                            <div className="text-lg font-bold text-primary">{plan.emi_months}</div>
                            <div className="text-xs text-gray-500">months</div>
                          </div>
                          <div>
                            <div className="font-semibold text-gray-800">{formatCurrency(plan.monthly_amount)}/month</div>
                            <div className="text-xs text-gray-500">
                              Total: {formatCurrency(plan.total_amount)}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          {plan.is_featured && plan.marketing_label && (
                            <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded">
                              {plan.marketing_label}
                            </span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>

                  <button
                    onClick={() => {
                      setSelectedPackage(pkg);
                      setShowEMIModal(true);
                    }}
                    className="w-full mt-4 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors text-sm font-medium flex items-center justify-center gap-2"
                  >
                    <Eye className="w-4 h-4" />
                    View All EMI Plans
                  </button>
                </>
              ) : (
                <div className="text-center py-4 text-gray-500">
                  <CreditCard className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                  <p className="text-sm">No EMI plans configured</p>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* EMI Plans Modal */}
      {showEMIModal && selectedPackage && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <div>
                  <h2 className="text-xl font-bold text-gray-800">{selectedPackage.family_type_name} - EMI Plans</h2>
                  <p className="text-gray-600">{selectedPackage.destination_category} • Total: {formatCurrency(selectedPackage.total_price)}</p>
                </div>
                <button
                  onClick={() => setShowEMIModal(false)}
                  className="text-gray-400 hover:text-gray-600 text-2xl"
                >
                  ×
                </button>
              </div>

              {selectedPackage.emi_plans.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {selectedPackage.emi_plans.map((plan) => (
                    <div key={plan.id} className={`border rounded-lg p-4 ${plan.is_featured ? 'border-primary bg-primary-light' : 'border-gray-200'}`}>
                      <div className="flex justify-between items-start mb-3">
                        <div>
                          <div className="text-lg font-bold text-gray-800">{plan.emi_months} Months Plan</div>
                          {plan.marketing_label && (
                            <span className="text-sm font-medium text-primary">{plan.marketing_label}</span>
                          )}
                        </div>
                        {plan.is_featured && (
                          <Star className="w-5 h-5 text-yellow-500 fill-current" />
                        )}
                      </div>

                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Monthly EMI:</span>
                          <span className="font-semibold">{formatCurrency(plan.monthly_amount)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Processing Fee:</span>
                          <span>{formatCurrency(plan.processing_fee)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Total Interest:</span>
                          <span>{formatCurrency(plan.total_interest)}</span>
                        </div>
                        <div className="flex justify-between font-semibold border-t pt-2">
                          <span>Total Amount:</span>
                          <span>{formatCurrency(plan.total_amount)}</span>
                        </div>
                        {plan.effective_annual_rate && (
                          <div className="flex justify-between text-xs text-gray-500">
                            <span>Effective APR:</span>
                            <span>{plan.effective_annual_rate}%</span>
                          </div>
                        )}
                      </div>

                      <button className="w-full mt-4 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors text-sm font-medium">
                        Select This Plan
                      </button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <CreditCard className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-800 mb-2">No EMI Plans Available</h3>
                  <p className="text-gray-600">EMI plans have not been configured for this package yet.</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Pagination Controls */}
      {!loading && !error && totalPages > 1 && (
        <div className="flex justify-center items-center gap-4 mt-8 mb-6">
          <button
            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            disabled={currentPage === 1}
            className={`px-3 py-2 rounded-lg text-sm font-medium ${
              currentPage === 1 
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
          >
            Previous
          </button>

          <div className="flex items-center gap-2">
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              let pageNum;
              if (totalPages <= 5) {
                pageNum = i + 1;
              } else if (currentPage <= 3) {
                pageNum = i + 1;
              } else if (currentPage >= totalPages - 2) {
                pageNum = totalPages - 4 + i;
              } else {
                pageNum = currentPage - 2 + i;
              }

              return (
                <button
                  key={pageNum}
                  onClick={() => setCurrentPage(pageNum)}
                  className={`px-3 py-2 rounded-lg text-sm font-medium ${
                    currentPage === pageNum
                      ? 'bg-primary text-white'
                      : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  {pageNum}
                </button>
              );
            })}
          </div>

          <button
            onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
            disabled={currentPage === totalPages}
            className={`px-3 py-2 rounded-lg text-sm font-medium ${
              currentPage === totalPages 
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
          >
            Next
          </button>

          <div className="ml-4 text-sm text-gray-600">
            Page {currentPage} of {totalPages}
          </div>
        </div>
      )}

      {/* Empty State */}
      {!loading && !error && filteredPackages.length === 0 && (
        <div className="text-center py-12">
          <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-800 mb-2">No EMI Packages Found</h3>
          <p className="text-gray-600">
            {packages.length === 0 
              ? 'No family EMI packages are available in the database.' 
              : 'Try adjusting your search or filter criteria.'}
          </p>
          {packages.length === 0 && (
            <button
              onClick={refreshData}
              className="mt-4 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
            >
              Refresh Data
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default FamilyEMIPackages; 